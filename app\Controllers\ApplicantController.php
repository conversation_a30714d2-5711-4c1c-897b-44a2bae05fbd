<?php

namespace App\Controllers;

class ApplicantController extends BaseController
{
    protected $session;
    protected $applicantsModel;
    protected $experiencesModel;
    protected $educationModel;
    protected $educationLevelsModel;
    protected $filesModel;

    public function __construct()
    {
        helper(['form', 'url', 'info', 'application']);
        $this->session = session();
        $this->applicantsModel = new \App\Models\ApplicantsModel();
        $this->experiencesModel = new \App\Models\ApplicantsExperiencesModel();
        $this->educationModel = new \App\Models\ApplicantEducationModel();
        $this->educationLevelsModel = new \App\Models\EducationLevelsModel();
        $this->filesModel = new \App\Models\ApplicantFilesModel();
    }

    public function dashboard()
    {
        // Get applicant ID from session
        $applicant_id = session()->get('applicant_id');
        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to access your dashboard');
        }

        // Get applicant data from database
        $applicant = $this->applicantsModel->find($applicant_id);
        if (!$applicant) {
            return redirect()->to('applicant/login')->with('error', 'Applicant not found');
        }

        // Prepare applicant info for dashboard (use same keys as before for view compatibility)
        $applicant_dashboard = [
            'fname' => $applicant['first_name'] ?? '',
            'lname' => $applicant['last_name'] ?? '',
            'email' => $applicant['email'] ?? '',
            'gender' => $applicant['gender'] ?? '',
            'dobirth' => $applicant['dobirth'] ?? '',
            'place_of_origin' => $applicant['place_of_origin'] ?? '',
            'contact_details' => $applicant['contact_details'] ?? '',
            'location_address' => $applicant['location_address'] ?? ''
        ];

        // Load application details model
        $applicationModel = new \App\Models\AppxApplicationDetailsModel();
        $applications = $applicationModel->getApplicationsByApplicantId($applicant_id);

        // Application statistics
        $total_applications = count($applications);
        $pending_applications = 0;
        $shortlisted_applications = 0;
        $rejected_applications = 0;
        foreach ($applications as $app) {
            $status = strtolower($app['application_status'] ?? '');
            if ($status === 'pending' || $status === 'pending_prescreen') {
                $pending_applications++;
            } elseif ($status === 'shortlisted') {
                $shortlisted_applications++;
            } elseif ($status === 'rejected' || $status === 'failed') {
                $rejected_applications++;
            }
        }

        // Recent applications (latest 5)
        usort($applications, function($a, $b) {
            return strtotime($b['created_at']) <=> strtotime($a['created_at']);
        });
        $recent_applications = array_slice($applications, 0, 5);
        // Map to view format
        $recent_applications = array_map(function($app) {
            return [
                'id' => $app['id'],
                'position_title' => $app['current_position'] ?? ($app['position_title'] ?? 'Position'),
                'department' => $app['current_employer'] ?? ($app['department'] ?? ''),
                'created_at' => $app['created_at'],
                'status' => $app['application_status'] ?? 'pending',
            ];
        }, $recent_applications);

        // Latest job openings (from published exercises and active positions)
        $positionsModel = new \App\Models\PositionsModel();
        $latest_jobs_raw = $positionsModel->select('positions.id, positions.designation as title, positions.location, positions.created_at as posted_date, dakoii_org.org_name as department')
            ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
            ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
            ->join('dakoii_org', 'positions.org_id = dakoii_org.id', 'left')
            ->where('positions.status', 'active')
            ->where('exercises.status', 'published')
            ->orderBy('positions.created_at', 'DESC')
            ->limit(5)
            ->findAll();
        $latest_jobs = array_map(function($job) {
            return [
                'id' => $job['id'],
                'title' => $job['title'],
                'department' => $job['department'],
                'location' => $job['location'],
                'posted_date' => $job['posted_date'],
            ];
        }, $latest_jobs_raw);

        return view('applicant/applicant_dashboard', [
            'title' => 'Dashboard',
            'menu' => 'dashboard',
            'applicant' => $applicant_dashboard,
            'total_applications' => $total_applications,
            'pending_applications' => $pending_applications,
            'shortlisted_applications' => $shortlisted_applications,
            'rejected_applications' => $rejected_applications,
            'recent_applications' => $recent_applications,
            'latest_jobs' => $latest_jobs
        ]);
    }

    public function profile()
    {
        // Get applicant ID from session
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to access your profile');
        }

        // Get applicant data from database
        $applicant = $this->applicantsModel->find($applicant_id);

        if (!$applicant) {
            return redirect()->to('applicant/login')->with('error', 'Applicant not found');
        }

        // Get work experiences from database
        $experiences = $this->experiencesModel->where('applicant_id', $applicant_id)
                                             ->orderBy('date_from', 'DESC')
                                             ->findAll();

        // Get education records from database
        $education = $this->educationModel->where('applicant_id', $applicant_id)
                                         ->orderBy('date_from', 'DESC')
                                         ->findAll();

        // Get education levels from database
        $education_data = $this->educationLevelsModel->getActiveEducationLevels();

        // Create education levels array for backward compatibility
        $education_levels = [];
        foreach ($education_data as $level) {
            $education_levels[$level['id']] = $level['name'];
        }

        // Get applicant files from database
        $files = $this->filesModel->where('applicant_id', $applicant_id)
                                 ->orderBy('created_at', 'DESC')
                                 ->findAll();

        return view('applicant/applicant_profile', [
            'title' => 'Profile',
            'menu' => 'profile',
            'applicant' => $applicant,
            'experiences' => $experiences,
            'education' => $education,
            'education_levels' => $education_levels,
            'education_data' => $education_data,
            'files' => $files
        ]);
    }

    // Personal Information Update
    public function updatePersonal()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        $validation = \Config\Services::validation();
        $validation->setRules([
            'first_name' => 'required|min_length[2]|max_length[50]',
            'last_name' => 'required|min_length[2]|max_length[50]',
            'gender' => 'required|in_list[Male,Female]',
            'dobirth' => 'required|valid_date',
            'contact_details' => 'required|min_length[5]',
            'location_address' => 'permit_empty|max_length[500]',
            'place_of_origin' => 'permit_empty|max_length[255]',
            'citizenship' => 'permit_empty|max_length[100]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        $data = [
            'first_name' => $this->request->getPost('first_name'),
            'last_name' => $this->request->getPost('last_name'),
            'gender' => $this->request->getPost('gender'),
            'dobirth' => $this->request->getPost('dobirth'),
            'contact_details' => $this->request->getPost('contact_details'),
            'location_address' => $this->request->getPost('location_address'),
            'place_of_origin' => $this->request->getPost('place_of_origin'),
            'citizenship' => $this->request->getPost('citizenship'),
            'updated_by' => $applicant_id
        ];

        try {
            $this->applicantsModel->update($applicant_id, $data);

            // Update session name
            session()->set('applicant_name', trim($data['first_name'] . ' ' . $data['last_name']));

            // Handle scroll position restoration
            $scrollPosition = $this->request->getPost('scroll_position');
            $redirectUrl = 'applicant/profile#personalInfo';
            if ($scrollPosition) {
                $redirectUrl .= '?scroll=' . $scrollPosition;
            }

            return redirect()->to($redirectUrl)->with('success', 'Personal information updated successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error updating personal information: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'Error updating personal information. Please try again.');
        }
    }

    // Documents Update
    public function updateDocuments()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        $data = [
            'id_numbers' => $this->request->getPost('id_numbers'),
            'offence_convicted' => $this->request->getPost('offence_convicted'),
            'updated_by' => $applicant_id
        ];

        try {
            $this->applicantsModel->update($applicant_id, $data);
            return redirect()->to('applicant/profile#documents')->with('success', 'Documents updated successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error updating documents: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error updating documents. Please try again.');
        }
    }

    // Employment Update
    public function updateEmployment()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        $data = [
            'current_employer' => $this->request->getPost('current_employer'),
            'current_position' => $this->request->getPost('current_position'),
            'current_salary' => $this->request->getPost('current_salary'),
            'how_did_you_hear_about_us' => $this->request->getPost('how_did_you_hear_about_us'),
            'updated_by' => $applicant_id
        ];

        try {
            $this->applicantsModel->update($applicant_id, $data);
            return redirect()->to('applicant/profile#employment')->with('success', 'Employment information updated successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error updating employment: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error updating employment information. Please try again.');
        }
    }

    // Add Experience
    public function addExperience()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        $validation = \Config\Services::validation();
        $validation->setRules([
            'employer' => 'required|min_length[2]|max_length[255]',
            'position' => 'required|min_length[2]|max_length[255]',
            'date_from' => 'required|valid_date',
            'date_to' => 'permit_empty|valid_date',
            'employer_contacts_address' => 'permit_empty|max_length[500]',
            'work_description' => 'permit_empty|max_length[1000]',
            'achievements' => 'permit_empty|max_length[1000]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        $data = [
            'applicant_id' => $applicant_id,
            'employer' => $this->request->getPost('employer'),
            'position' => $this->request->getPost('position'),
            'date_from' => $this->request->getPost('date_from'),
            'date_to' => $this->request->getPost('date_to') ?: null,
            'employer_contacts_address' => $this->request->getPost('employer_contacts_address'),
            'work_description' => $this->request->getPost('work_description'),
            'achievements' => $this->request->getPost('achievements'),
            'created_by' => $applicant_id
        ];

        try {
            $this->experiencesModel->insert($data);
            return redirect()->to('applicant/profile#experiences')->with('success', 'Work experience added successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error adding experience: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'Error adding work experience. Please try again.');
        }
    }

    // Update Experience
    public function updateExperience()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        $experience_id = $this->request->getPost('id');

        // Verify ownership
        $experience = $this->experiencesModel->where('id', $experience_id)
                                           ->where('applicant_id', $applicant_id)
                                           ->first();

        if (!$experience) {
            return redirect()->back()->with('error', 'Experience not found or access denied');
        }

        $validation = \Config\Services::validation();
        $validation->setRules([
            'employer' => 'required|min_length[2]|max_length[255]',
            'position' => 'required|min_length[2]|max_length[255]',
            'date_from' => 'required|valid_date',
            'date_to' => 'permit_empty|valid_date',
            'employer_contacts_address' => 'permit_empty|max_length[500]',
            'work_description' => 'permit_empty|max_length[1000]',
            'achievements' => 'permit_empty|max_length[1000]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        $data = [
            'employer' => $this->request->getPost('employer'),
            'position' => $this->request->getPost('position'),
            'date_from' => $this->request->getPost('date_from'),
            'date_to' => $this->request->getPost('date_to') ?: null,
            'employer_contacts_address' => $this->request->getPost('employer_contacts_address'),
            'work_description' => $this->request->getPost('work_description'),
            'achievements' => $this->request->getPost('achievements'),
            'updated_by' => $applicant_id
        ];

        try {
            $this->experiencesModel->update($experience_id, $data);
            return redirect()->to('applicant/profile#experiences')->with('success', 'Work experience updated successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error updating experience: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error updating work experience. Please try again.');
        }
    }

    // Delete Experience
    public function deleteExperience($experience_id)
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        // Verify ownership
        $experience = $this->experiencesModel->where('id', $experience_id)
                                           ->where('applicant_id', $applicant_id)
                                           ->first();

        if (!$experience) {
            return redirect()->back()->with('error', 'Experience not found or access denied');
        }

        try {
            $this->experiencesModel->delete($experience_id);
            return redirect()->to('applicant/profile#experiences')->with('success', 'Work experience deleted successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error deleting experience: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error deleting work experience. Please try again.');
        }
    }

    // Add Education
    public function addEducation()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        $validation = \Config\Services::validation();
        $validation->setRules([
            'institution' => 'required|min_length[2]|max_length[255]',
            'course' => 'required|min_length[2]|max_length[255]',
            'education_level' => 'required|numeric',
            'date_from' => 'required|valid_date',
            'date_to' => 'permit_empty|valid_date',
            'units' => 'permit_empty|max_length[1000]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        $data = [
            'applicant_id' => $applicant_id,
            'institution' => $this->request->getPost('institution'),
            'course' => $this->request->getPost('course'),
            'education_level' => $this->request->getPost('education_level'),
            'date_from' => $this->request->getPost('date_from'),
            'date_to' => $this->request->getPost('date_to') ?: null,
            'units' => $this->request->getPost('units'),
            'created_by' => $applicant_id
        ];

        try {
            $this->educationModel->insert($data);
            return redirect()->to('applicant/profile#education')->with('success', 'Education record added successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error adding education: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'Error adding education record. Please try again.');
        }
    }

    // Update Education
    public function updateEducation()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        $education_id = $this->request->getPost('id');

        // Verify ownership
        $education = $this->educationModel->where('id', $education_id)
                                        ->where('applicant_id', $applicant_id)
                                        ->first();

        if (!$education) {
            return redirect()->back()->with('error', 'Education record not found or access denied');
        }

        $validation = \Config\Services::validation();
        $validation->setRules([
            'institution' => 'required|min_length[2]|max_length[255]',
            'course' => 'required|min_length[2]|max_length[255]',
            'education_level' => 'required|numeric',
            'date_from' => 'required|valid_date',
            'date_to' => 'permit_empty|valid_date',
            'units' => 'permit_empty|max_length[1000]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        $data = [
            'institution' => $this->request->getPost('institution'),
            'course' => $this->request->getPost('course'),
            'education_level' => $this->request->getPost('education_level'),
            'date_from' => $this->request->getPost('date_from'),
            'date_to' => $this->request->getPost('date_to') ?: null,
            'units' => $this->request->getPost('units'),
            'updated_by' => $applicant_id
        ];

        try {
            $this->educationModel->update($education_id, $data);
            return redirect()->to('applicant/profile#education')->with('success', 'Education record updated successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error updating education: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error updating education record. Please try again.');
        }
    }

    // Delete Education
    public function deleteEducation($education_id)
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        // Verify ownership
        $education = $this->educationModel->where('id', $education_id)
                                        ->where('applicant_id', $applicant_id)
                                        ->first();

        if (!$education) {
            return redirect()->back()->with('error', 'Education record not found or access denied');
        }

        try {
            $this->educationModel->delete($education_id);
            return redirect()->to('applicant/profile#education')->with('success', 'Education record deleted successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error deleting education: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error deleting education record. Please try again.');
        }
    }

    // Update Family Information
    public function updateFamily()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        // Process children data
        $children_data = $this->request->getPost('children');
        $children_json = null;

        if ($children_data && is_array($children_data)) {
            // Filter out empty children entries
            $filtered_children = array_filter($children_data, function($child) {
                return !empty($child['name']) || !empty($child['dob']) || !empty($child['gender']);
            });

            if (!empty($filtered_children)) {
                $children_json = json_encode(array_values($filtered_children));
            }
        }

        $data = [
            'marital_status' => $this->request->getPost('marital_status'),
            'date_of_marriage' => $this->request->getPost('date_of_marriage') ?: null,
            'spouse_employer' => $this->request->getPost('spouse_employer'),
            'children' => $children_json,
            'referees' => $this->request->getPost('referees'),
            'updated_by' => $applicant_id
        ];

        // All fields are valid according to the model's allowedFields, so no filtering needed

        try {
            $this->applicantsModel->update($applicant_id, $data);
            return redirect()->to('applicant/profile#family')->with('success', 'Family information updated successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error updating family information: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error updating family information. Please try again.');
        }
    }

    // Update Additional Information
    public function updateAdditional()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        $data = [
            'publications' => $this->request->getPost('publications'),
            'awards' => $this->request->getPost('awards'),
            'referees' => $this->request->getPost('referees'),
            'updated_by' => $applicant_id
        ];

        try {
            $this->applicantsModel->update($applicant_id, $data);

            // Get scroll position from session if available
            $scrollPosition = $this->request->getPost('scroll_position');
            $redirectUrl = 'applicant/profile#additional';
            if ($scrollPosition) {
                $redirectUrl .= '?scroll=' . $scrollPosition;
            }

            return redirect()->to($redirectUrl)->with('success', 'Additional information updated successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error updating additional information: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error updating additional information. Please try again.');
        }
    }

    // Upload File
    public function uploadFile()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        // Debug: Log the request data
        log_message('debug', 'Upload file request - Applicant ID: ' . $applicant_id);
        log_message('debug', 'POST data: ' . json_encode($this->request->getPost()));

        $validation = \Config\Services::validation();
        $validation->setRules([
            'file_title' => 'required|min_length[2]|max_length[255]',
            'file_description' => 'permit_empty|max_length[500]',
            'file' => 'uploaded[file]|max_size[file,25600]|ext_in[file,pdf,doc,docx,jpg,jpeg,png]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            $errors = $validation->getErrors();
            log_message('error', 'File upload validation failed: ' . json_encode($errors));
            return redirect()->back()->withInput()->with('errors', $errors);
        }

        $file = $this->request->getFile('file');

        // Debug: Log file information
        if ($file) {
            log_message('debug', 'File info - Name: ' . $file->getName() . ', Size: ' . $file->getSize() . ', Type: ' . $file->getMimeType());
            log_message('debug', 'File valid: ' . ($file->isValid() ? 'Yes' : 'No'));
            log_message('debug', 'File moved: ' . ($file->hasMoved() ? 'Yes' : 'No'));
            if (!$file->isValid()) {
                log_message('error', 'File error: ' . $file->getErrorString());
            }
        } else {
            log_message('error', 'No file received in request');
        }

        if ($file && $file->isValid() && !$file->hasMoved()) {
            // Get file information BEFORE moving the file
            $originalName = $file->getName();
            $newName = $file->getRandomName();
            $uploadPath = FCPATH . 'uploads/applicants/' . $applicant_id . '/';

            // Try to get MIME type safely
            $mimeType = null;
            try {
                $mimeType = $file->getMimeType();
            } catch (\Exception $e) {
                log_message('warning', 'Could not get MIME type from uploaded file: ' . $e->getMessage());
                // Will be determined later from file extension
            }

            // Debug: Log upload path and file info
            log_message('debug', 'Upload path: ' . $uploadPath);
            log_message('debug', 'Original file: ' . $originalName . ', MIME: ' . ($mimeType ?: 'unknown'));

            // Create directory if it doesn't exist
            if (!is_dir($uploadPath)) {
                if (!mkdir($uploadPath, 0755, true)) {
                    log_message('error', 'Failed to create upload directory: ' . $uploadPath);
                    return redirect()->back()->with('error', 'Error creating upload directory. Please try again.');
                }
                log_message('debug', 'Created upload directory: ' . $uploadPath);
            }

            // Check if directory is writable
            if (!is_writable($uploadPath)) {
                log_message('error', 'Upload directory is not writable: ' . $uploadPath);
                return redirect()->back()->with('error', 'Upload directory is not writable. Please contact administrator.');
            }

            if ($file->move($uploadPath, $newName)) {
                log_message('debug', 'File moved successfully to: ' . $uploadPath . $newName);

                // Get file path for Gemini AI
                $filePath = $uploadPath . $newName;

                // Load the Gemini helper
                helper('GeminiAI');

                // Extract text using simple Gemini function
                log_message('info', "Starting text extraction for file: {$newName}");
                $extractionResult = gemini_extract_text_from_file($filePath);

                $extractedText = null;
                $extractionStatus = 'failed';

                if ($extractionResult['success']) {
                    $extractedText = $extractionResult['extracted_text'];
                    $extractionStatus = 'completed';
                    log_message('info', 'Text extraction successful for file: ' . $newName . ' - ' . strlen($extractedText) . ' characters extracted');
                } else {
                    log_message('error', 'Text extraction failed for file: ' . $newName . ' - ' . $extractionResult['message']);
                }

                $data = [
                    'applicant_id' => $applicant_id,
                    'file_title' => $this->request->getPost('file_title'),
                    'file_description' => $this->request->getPost('file_description'),
                    'file_path' => 'public/uploads/applicants/' . $applicant_id . '/' . $newName,
                    'file_extracted_texts' => $extractedText,
                    'created_by' => $applicant_id
                ];

                // Debug: Log data to be inserted
                log_message('debug', 'Data to insert: ' . json_encode(array_merge($data, ['file_extracted_texts' => $extractedText ? substr($extractedText, 0, 100) . '...' : null])));

                try {
                    $result = $this->filesModel->insert($data);
                    log_message('debug', 'File record inserted with ID: ' . $result);

                    // Handle scroll position restoration
                    $scrollPosition = $this->request->getPost('scroll_position');
                    $redirectUrl = 'applicant/profile#files';
                    if ($scrollPosition) {
                        $redirectUrl .= '?scroll=' . $scrollPosition;
                    }

                    // Set success message
                    if ($extractionResult['success']) {
                        $successMessage = 'File uploaded and text extracted successfully!';
                    } else {
                        $successMessage = 'File uploaded successfully, but text extraction failed: ' . $extractionResult['message'];
                    }

                    return redirect()->to($redirectUrl)->with('success', $successMessage);
                } catch (\Exception $e) {
                    log_message('error', 'Error saving file record: ' . $e->getMessage());
                    // Delete the uploaded file if database insert fails
                    if (file_exists($uploadPath . $newName)) {
                        unlink($uploadPath . $newName);
                    }
                    return redirect()->back()->with('error', 'Error saving file record. Please try again.');
                }
            } else {
                log_message('error', 'Failed to move file to: ' . $uploadPath . $newName);
                return redirect()->back()->with('error', 'Error uploading file. Please try again.');
            }
        } else {
            $errorMsg = 'Invalid file or file upload error.';
            if ($file) {
                $errorMsg .= ' Error: ' . $file->getErrorString();
            }
            log_message('error', $errorMsg);
            return redirect()->back()->with('error', $errorMsg);
        }
    }

    // Update File
    public function updateFile()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        $file_id = $this->request->getPost('file_id');

        // Verify ownership
        $file = $this->filesModel->where('id', $file_id)
                                ->where('applicant_id', $applicant_id)
                                ->first();

        if (!$file) {
            return redirect()->back()->with('error', 'File not found or access denied');
        }

        $validation = \Config\Services::validation();
        $validation->setRules([
            'file_title' => 'required|min_length[2]|max_length[255]',
            'file_description' => 'permit_empty|max_length[500]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        $data = [
            'file_title' => $this->request->getPost('file_title'),
            'file_description' => $this->request->getPost('file_description'),
            'updated_by' => $applicant_id
        ];

        try {
            $this->filesModel->update($file_id, $data);

            // Handle scroll position restoration
            $scrollPosition = $this->request->getPost('scroll_position');
            $redirectUrl = 'applicant/profile#files';
            if ($scrollPosition) {
                $redirectUrl .= '?scroll=' . $scrollPosition;
            }

            return redirect()->to($redirectUrl)->with('success', 'File information updated successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error updating file: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error updating file information. Please try again.');
        }
    }

    // Delete File
    public function deleteFile($file_id)
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        // Verify ownership
        $file = $this->filesModel->where('id', $file_id)
                                ->where('applicant_id', $applicant_id)
                                ->first();

        if (!$file) {
            return redirect()->back()->with('error', 'File not found or access denied');
        }

        try {
            // Delete physical file (remove 'public/' prefix for file system path)
            $physicalPath = str_replace('public/', FCPATH, $file['file_path']);
            if (file_exists($physicalPath)) {
                unlink($physicalPath);
            }

            // Delete database record
            $this->filesModel->delete($file_id);

            // Handle scroll position restoration (from localStorage since this is called via JS)
            return redirect()->to('applicant/profile#files')->with('success', 'File deleted successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error deleting file: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error deleting file. Please try again.');
        }
    }

    // Change Password
    public function changePassword()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        $validation = \Config\Services::validation();
        $validation->setRules([
            'current_password' => 'required',
            'new_password' => 'required|min_length[6]',
            'confirm_password' => 'required|matches[new_password]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        // Get current applicant data
        $applicant = $this->applicantsModel->find($applicant_id);

        if (!$applicant) {
            return redirect()->back()->with('error', 'Applicant not found');
        }

        // Verify current password
        if (!password_verify($this->request->getPost('current_password'), $applicant['password'])) {
            return redirect()->back()->with('error', 'Current password is incorrect');
        }

        $data = [
            'password' => $this->request->getPost('new_password'), // Will be hashed by model callback
            'updated_by' => $applicant_id
        ];

        try {
            $this->applicantsModel->update($applicant_id, $data);
            return redirect()->to('applicant/profile#security')->with('success', 'Password changed successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error changing password: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error changing password. Please try again.');
        }
    }

    // Upload Photo
    public function uploadPhoto()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        $validation = \Config\Services::validation();
        $validation->setRules([
            'id_photo' => 'uploaded[id_photo]|max_size[id_photo,2048]|ext_in[id_photo,jpg,jpeg,png]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->with('errors', $validation->getErrors());
        }

        $file = $this->request->getFile('id_photo');

        if ($file->isValid() && !$file->hasMoved()) {
            $newName = 'photo_' . $applicant_id . '_' . time() . '.' . $file->getExtension();
            $uploadPath = FCPATH . 'uploads/applicants/' . $applicant_id . '/';

            // Create directory if it doesn't exist
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            if ($file->move($uploadPath, $newName)) {
                $data = [
                    'id_photo_path' => 'public/uploads/applicants/' . $applicant_id . '/' . $newName,
                    'updated_by' => $applicant_id
                ];

                try {
                    $this->applicantsModel->update($applicant_id, $data);
                    return redirect()->to('applicant/profile')->with('success', 'Profile photo updated successfully');
                } catch (\Exception $e) {
                    log_message('error', 'Error updating photo: ' . $e->getMessage());
                    return redirect()->back()->with('error', 'Error updating profile photo. Please try again.');
                }
            } else {
                return redirect()->back()->with('error', 'Error uploading photo. Please try again.');
            }
        } else {
            return redirect()->back()->with('error', 'Invalid photo file or upload error.');
        }
    }



    // AJAX File Upload with Progress Tracking
    public function uploadFileAjax()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Please login to continue'
            ]);
        }

        // Validation
        $validation = \Config\Services::validation();
        $validation->setRules([
            'file_title' => 'required|min_length[2]|max_length[255]',
            'file_description' => 'permit_empty|max_length[500]',
            'file' => 'uploaded[file]|max_size[file,25600]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validation->getErrors()
            ]);
        }

        $file = $this->request->getFile('file');

        if (!$file->isValid() || $file->hasMoved()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid file or file upload error'
            ]);
        }

        try {
            // Generate unique process ID for tracking
            $processId = uniqid('upload_', true);

            // Store initial progress
            session()->set("upload_progress_{$processId}", [
                'step' => 'uploading',
                'progress' => 10,
                'message' => 'Uploading file...',
                'file_name' => $file->getClientName()
            ]);

            // Create upload directory
            $uploadPath = FCPATH . 'uploads/applicants/' . $applicant_id . '/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            // Generate unique filename
            $newName = 'file_' . $applicant_id . '_' . time() . '_' . uniqid() . '.' . $file->getExtension();

            // Move file
            if (!$file->move($uploadPath, $newName)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to upload file'
                ]);
            }

            // Update progress
            session()->set("upload_progress_{$processId}", [
                'step' => 'analyzing',
                'progress' => 30,
                'message' => 'Analyzing document...',
                'file_name' => $file->getClientName()
            ]);

            // Get file path for processing
            $filePath = $uploadPath . $newName;

            // Start text extraction in background
            $this->startAjaxTextExtraction($processId, $filePath, $applicant_id, $file, $newName);

            return $this->response->setJSON([
                'success' => true,
                'process_id' => $processId,
                'message' => 'File uploaded successfully. Processing started.'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error in AJAX file upload: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error uploading file: ' . $e->getMessage()
            ]);
        }
    }

    // Start AJAX text extraction process
    private function startAjaxTextExtraction($processId, $filePath, $applicantId, $file, $newName)
    {
        // Load the Gemini helper
        helper('GeminiAI');

        // Update progress
        session()->set("upload_progress_{$processId}", [
            'step' => 'processing',
            'progress' => 40,
            'message' => 'Starting AI text extraction...',
            'file_name' => $file->getClientName()
        ]);

        // Extract text using Gemini AI with progress tracking
        $extractionResult = gemini_extract_text_from_file($filePath, $processId);

        $extractedText = null;
        $extractionStatus = 'failed';

        if ($extractionResult['success']) {
            $extractedText = $extractionResult['extracted_text'];
            $extractionStatus = 'completed';

            // Update progress for successful extraction
            session()->set("upload_progress_{$processId}", [
                'step' => 'saving',
                'progress' => 90,
                'message' => 'Saving to database...',
                'file_name' => $file->getClientName()
            ]);
        } else {
            // Update progress for failed extraction
            session()->set("upload_progress_{$processId}", [
                'step' => 'error',
                'progress' => 50,
                'message' => 'Text extraction failed: ' . $extractionResult['message'],
                'file_name' => $file->getClientName(),
                'error' => $extractionResult['message']
            ]);
        }

        // Save to database
        $data = [
            'applicant_id' => $applicantId,
            'file_title' => $this->request->getPost('file_title'),
            'file_description' => $this->request->getPost('file_description'),
            'file_path' => 'public/uploads/applicants/' . $applicantId . '/' . $newName,
            'file_extracted_texts' => $extractedText,
            'created_by' => $applicantId
        ];

        try {
            $result = $this->filesModel->insert($data);

            // Final progress update
            session()->set("upload_progress_{$processId}", [
                'step' => 'completed',
                'progress' => 100,
                'message' => 'File processed successfully!',
                'file_name' => $file->getClientName(),
                'extraction_status' => $extractionStatus,
                'file_id' => $result
            ]);

        } catch (\Exception $e) {
            session()->set("upload_progress_{$processId}", [
                'step' => 'error',
                'progress' => 95,
                'message' => 'Database error: ' . $e->getMessage(),
                'file_name' => $file->getClientName(),
                'error' => $e->getMessage()
            ]);
        }
    }

    // Check upload progress
    public function checkUploadProgress($processId)
    {
        $progress = session()->get("upload_progress_{$processId}");

        if (!$progress) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Process not found'
            ]);
        }

        return $this->response->setJSON([
            'success' => true,
            'progress' => $progress
        ]);
    }

    // Note: serveFile method removed since files are now stored in public directory
    // and can be accessed directly via base_url()
}